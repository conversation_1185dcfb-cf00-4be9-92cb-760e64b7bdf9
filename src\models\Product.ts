import mongoose from 'mongoose';

const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  description: {
    en: {
      type: String,
      required: true,
    },
    fr: {
      type: String,
    },
    it: {
      type: String,
    },
  },
  price: {
    type: Number,
    required: true,
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: true,
  },
  subcategory: {
    type: mongoose.Schema.Types.ObjectId,
    // Note: subcategories are subdocuments within categories, not separate documents
    // Population is handled manually in API routes
  },
  images: {
    type: [String],
    default: [],
  },
  imageUrl: {
    type: String,
  },
  videoUrl: {
    type: String,
  },
  inventory: {
    inStock: {
      type: Number,
      default: 0
    }
  },
  weight: {
    type: Number,
    required: true,
  },
  shape: {
    type: String,
    enum: ['Oval', 'Round', 'Pear', 'Emerald', 'Marquise', 'Radiant', 'Square', 'Heart', 'Cushion', 'Trilliant', 'Octagonal', 'Triangular'],
  },
  isFeatured: {
    type: Boolean,
    default: false,
  },
  isLatest: {
    type: Boolean,
    default: false,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

export const Product = mongoose.models.Product || mongoose.model('Product', productSchema); 