import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { Product } from '@/models/Product';
import { parse } from 'csv-parse';
import mongoose from 'mongoose';

// Required fields for validation
const requiredFields = ['name', 'description', 'price', 'category'];

// Function to set a nested property in an object
const setNestedProperty = (obj: any, path: string, value: any) => {
  const parts = path.split('.');
  let current = obj;
  
  for (let i = 0; i < parts.length - 1; i++) {
    const part = parts[i];
    if (!current[part]) {
      current[part] = {};
    }
    current = current[part];
  }
  
  current[parts[parts.length - 1]] = value;
};

export async function POST(request: NextRequest) {
  try {
    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      return NextResponse.json({
        success: false,
        error: 'Failed to connect to the database',
        details: dbConnection.error
      }, { status: 500 });
    }

    // Get the file from the request
    const formData = await request.formData();
    const file = formData.get('file');

    if (!file || !(file instanceof File)) {
      return NextResponse.json({ 
        success: false, 
        error: 'No file was uploaded or invalid file' 
      }, { status: 400 });
    }

    // Check file type
    if (!file.name.endsWith('.csv')) {
      return NextResponse.json({ 
        success: false, 
        error: 'Uploaded file must be a CSV file' 
      }, { status: 400 });
    }

    // Read file content
    const fileContent = await file.text();
    
    // Parse CSV content
    const records: any[] = await new Promise((resolve, reject) => {
      parse(fileContent, {
        columns: true,
        skip_empty_lines: true,
        trim: true,
        cast: true,
      }, (err, records) => {
        if (err) reject(err);
        else resolve(records);
      });
    });

    if (!records || records.length === 0) {
      return NextResponse.json({ 
        success: false, 
        error: 'No records found in the CSV file' 
      }, { status: 400 });
    }

    // Statistics tracking
    const stats = {
      total: records.length,
      created: 0,
      updated: 0,
      failed: 0,
      errorDetails: [] as string[]
    };

    // Process each record
    for (const record of records) {
      try {
        // Validate required fields
        const missingFields = requiredFields.filter(field => !record[field]);
        
        if (missingFields.length > 0) {
          throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
        }

        // Convert price to number if it's a string
        if (typeof record.price === 'string') {
          record.price = parseFloat(record.price);
        }

        // Check if price is a valid number
        if (isNaN(record.price)) {
          throw new Error('Price must be a valid number');
        }

        // Create a product object with base fields
        const productData: any = {
          name: record.name,
          description: record.description,
          price: record.price,
          category: record.category,
        };

        // Add optional fields if they exist
        if (record.imageUrl) productData.imageUrl = record.imageUrl;
        if (record.videoUrl) productData.videoUrl = record.videoUrl;

        // Initialize inventory object
        productData.inventory = {
          inStock: 0
        };

        // Process all fields that might be nested (like inventory.inStock)
        for (const [key, value] of Object.entries(record)) {
          if (key.includes('.') && value !== undefined && value !== '') {
            setNestedProperty(productData, key, 
              key.includes('inventory') ? parseInt(value as string) : value
            );
          }
        }

        // Determine if this is an update or a new product
        if (record._id) {
          // Validate that the ID is a valid ObjectId
          if (!mongoose.Types.ObjectId.isValid(record._id)) {
            throw new Error(`Invalid product ID: ${record._id}`);
          }

          // Update existing product
          const result = await Product.findByIdAndUpdate(
            record._id,
            productData,
            { new: true }
          );

          if (!result) {
            throw new Error(`Product with ID ${record._id} not found`);
          }

          stats.updated++;
        } else {
          // Create new product
          await Product.create(productData);
          stats.created++;
        }
      } catch (recordError: any) {
        stats.failed++;
        stats.errorDetails.push(`Row ${stats.failed + stats.created + stats.updated}: ${recordError.message}`);
      }
    }

    // Return statistics
    return NextResponse.json({
      success: true,
      message: `Processed ${stats.total} products: ${stats.created} created, ${stats.updated} updated, ${stats.failed} failed`,
      ...stats
    });
  } catch (error: any) {
    console.error('Error importing products:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to import products'
    }, { status: 500 });
  }
} 