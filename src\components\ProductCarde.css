.card {
  background: #f8f8f8;
  border-radius: 0.5rem;
  overflow: hidden;
  transition: box-shadow 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}
.card:hover {
  box-shadow: 0 10px 20px rgba(0,0,0,0.08), 0 2px 4px rgba(0,0,0,0.04);
}

.imageContainer {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.aspect {
  aspect-ratio: 4/3;
}
@media (min-width: 640px) {
  .aspect {
    aspect-ratio: 1/1;
  }
}

.productImage {
  object-fit: cover;
  transition: transform 0.3s;
}
.productImage:hover {
  transform: scale(1.05);
}

/* Add to cart button styles */
.cartIconContainer {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transform: translateY(-5px);
  transition: opacity 0.3s, transform 0.3s, background-color 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.cartIconContainer:hover {
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px) scale(1.1);
}

.cartIconContainer:active {
  background-color: rgba(245, 245, 245, 1);
  transform: scale(0.9);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Show on hover for desktop */
@media (min-width: 768px) {
  .card:hover .cartIconContainer {
    opacity: 1;
    transform: translateY(0);
  }
  
  .card:hover .cartIconContainer:hover {
    transform: scale(1.1);
  }
  
  .card:hover .cartIconContainer:active {
    transform: scale(0.9);
  }
}

/* Enable hover on touch devices */
@media (hover: hover) {
  .card:hover .cartIconContainer {
    opacity: 1;
    transform: translateY(0);
  }
  
  .card:hover .cartIconContainer:hover {
    transform: scale(1.1);
  }
  
  .card:hover .cartIconContainer:active {
    transform: scale(0.9);
  }
}

/* For mobile - visible when card is tapped */
.cartIconVisible {
  opacity: 1;
  transform: translateY(0);
}

.cartIconVisible:hover {
  transform: scale(1.1);
}

.cartIconVisible:active {
  transform: scale(0.9);
}

/* Play button styles */
.playButtonContainer {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 10;
  background: linear-gradient(to top, #4b5563, #6b7280);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.playButtonContainer:hover {
  background: linear-gradient(to top, #374151, #4b5563);
}

.playButtonContainer:active {
  transform: scale(0.7);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.playIcon {
  color: white;
}

.details {
  padding: 10px;
  flex: 1;
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 8px;
  align-items: start;
}
@media (min-width: 640px) {
  .details {
    padding: 12px;
  }
}
@media (min-width: 768px) {
  .details {
    padding: 16px;
  }
}

.productName {
  font-family: var(--font-dosis), sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.07em;
  text-transform: capitalize;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
@media (min-width: 640px) {
  .productName {
    font-size: 14px;
    line-height: 20px;
  }
}
@media (min-width: 768px) {
  .productName {
    font-size: 14px;
    line-height: 20px;
  }
}

.weight {
  font-family: var(--font-dosis), sans-serif;
  font-weight: 400;
  font-size: 13px;
  line-height: 20px;
  letter-spacing: 0.05em;
  color: #4b5563;
  margin-bottom: 0.25rem;
}
@media (min-width: 640px) {
  .weight {
    font-size: 13px;
    line-height: 20px;
  }
}

.priceRow {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  margin-top: 0.375rem;
}
@media (min-width: 640px) {
  .priceRow {
    gap: 0.5rem;
    margin-top: 0.5rem;
  }
}

.priceText {
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 500;
  color: #000;
}
@media (min-width: 640px) {
  .priceText {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}
@media (min-width: 768px) {
  .priceText {
    font-size: 1rem;
    line-height: 1.5rem;
  }
} 