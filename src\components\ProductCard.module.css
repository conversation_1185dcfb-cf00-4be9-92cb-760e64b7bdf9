.card {
  background: #f8f8f8;
  border-radius: 0.5rem;
  overflow: hidden;
  transition: box-shadow 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.card:hover {
  box-shadow: 0 10px 20px rgba(0,0,0,0.08), 0 2px 4px rgba(0,0,0,0.04);
}

.imageContainer {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.aspect {
  aspect-ratio: 4/3;
}
@media (min-width: 640px) {
  .aspect {
    aspect-ratio: 1/1;
  }
}

.productImage {
  object-fit: cover;
  transition: transform 0.3s;
}
.productImage:hover {
  transform: scale(1.05);
}

.details {
  padding: 10px;
  flex: 1;
  display: flex;
  flex-direction: row;
  gap: 8px;
}

.detailsLeft {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.detailsRight {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  gap: 4px;
  min-width: 60px;
}
@media (min-width: 640px) {
  .details {
    padding: 12px;
  }
}
@media (min-width: 768px) {
  .details {
    padding: 16px;
  }
}

.productName {
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.25rem;
  text-transform: capitalize;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
@media (min-width: 640px) {
  .productName {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}
@media (min-width: 768px) {
  .productName {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

.weight {
  font-size: 0.75rem;
  line-height: 1rem;
  color: #4b5563;
  margin-bottom: 0.25rem;
}
@media (min-width: 640px) {
  .weight {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

.priceRow {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  margin-top: 0.375rem;
}
@media (min-width: 640px) {
  .priceRow {
    gap: 0.5rem;
    margin-top: 0.5rem;
  }
}

.priceText {
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 500;
  color: #000;
}
@media (min-width: 640px) {
  .priceText {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}
@media (min-width: 768px) {
  .priceText {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.buttonContainer {
  margin-top: 0.625rem;
}
@media (min-width: 640px) {
  .buttonContainer {
    margin-top: 0.75rem;
  }
}
@media (min-width: 768px) {
  .buttonContainer {
    margin-top: 1rem;
  }
}

.buyButton {
  width: 100%;
  padding: 0.375rem 0.5rem;
  border-radius: 9999px;
  background: linear-gradient(135deg, #78002e, #a3003f);
  color: #fff;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.25rem;
  transition: box-shadow 0.2s, transform 0.2s, background 0.2s;
  outline: none;
  border: none;
}
@media (min-width: 640px) {
  .buyButton {
    font-size: 0.75rem;
    line-height: 1rem;
    padding: 0.5rem 0.75rem;
    gap: 0.375rem;
  }
}
@media (min-width: 768px) {
  .buyButton {
    font-size: 0.875rem;
    line-height: 1.25rem;
    padding: 0.5rem 1rem;
  }
}
.buyButton:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.12);
  transform: scale(1.02);
  background: linear-gradient(135deg, #660027, #8f0037);
}
.buyButton:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, #540020, #7a0030);
}
.buyButton:focus {
  box-shadow: 0 0 0 2px rgba(163,0,63,0.5);
}

.stockStatus {
  font-size: 0.625rem;
  line-height: 0.875rem;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid;
  text-align: center;
  white-space: nowrap;
}
@media (min-width: 640px) {
  .stockStatus {
    font-size: 0.75rem;
    line-height: 1rem;
    padding: 3px 8px;
  }
}

.stockQuantity {
  font-size: 0.625rem;
  line-height: 0.875rem;
  color: #6b7280;
  font-weight: 400;
  text-align: right;
  white-space: nowrap;
}
@media (min-width: 640px) {
  .stockQuantity {
    font-size: 0.75rem;
    line-height: 1rem;
  }
}

/* Cart Icon Styles */
.cartIconContainer {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 20;
  background: linear-gradient(to top, #78002e, #a3003f);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  opacity: 0;
  transform: translateY(-10px);
  color: white;
}

.cartIconContainer:hover {
  background: linear-gradient(to top, #660027, #8f0037);
}

.cartIconContainer:active {
  transform: scale(0.7);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Show on hover for desktop */
@media (min-width: 768px) {
  .card:hover .cartIconContainer {
    opacity: 1;
    transform: translateY(0);
  }

  .card:hover .cartIconContainer:hover {
    transform: scale(1.1);
  }

  .card:hover .cartIconContainer:active {
    transform: scale(0.9);
  }
}

/* Enable hover on touch devices */
@media (hover: hover) {
  .card:hover .cartIconContainer {
    opacity: 1;
    transform: translateY(0);
  }

  .card:hover .cartIconContainer:hover {
    transform: scale(1.1);
  }

  .card:hover .cartIconContainer:active {
    transform: scale(0.9);
  }
}

/* For mobile - visible when card is tapped */
.cartIconVisible {
  opacity: 1;
  transform: translateY(0);
}

.cartIconVisible:hover {
  transform: scale(1.1);
}

.cartIconVisible:active {
  transform: scale(0.9);
}

/* Play button styles */
.playButtonContainer {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 10;
  background: linear-gradient(to top, #4b5563, #6b7280);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.playButtonContainer:hover {
  background: linear-gradient(to top, #374151, #4b5563);
}

.playButtonContainer:active {
  transform: scale(0.7);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.playIcon {
  color: white;
}

.cartIcon {
  display: none;
}
@media (min-width: 640px) {
  .cartIcon {
    display: inline;
  }
}