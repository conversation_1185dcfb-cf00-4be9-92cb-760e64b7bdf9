import { Product } from '@/models/Product';

export interface StockInfo {
  inStock: number;
  status: 'in_stock' | 'out_of_stock';
  available: boolean;
}

export interface StockValidationResult {
  valid: boolean;
  message: string;
  availableStock: number;
  requestedQuantity: number;
}

/**
 * Get stock status for a given inventory
 */
export function getStockStatus(inventory?: { inStock: number }): StockInfo {
  const defaultInventory = { inStock: 0 };
  const inv = inventory || defaultInventory;

  let status: 'in_stock' | 'out_of_stock' = 'out_of_stock';
  let available = true;

  if (inv.inStock > 0) {
    status = 'in_stock';
  } else {
    status = 'out_of_stock';
    available = false;
  }

  return {
    inStock: inv.inStock,
    status,
    available
  };
}

/**
 * Validate if requested quantity is available in stock
 */
export function validateStockQuantity(
  inventory: { inStock: number } | undefined,
  requestedQuantity: number
): StockValidationResult {
  const inv = inventory || { inStock: 0 };
  const available = inv.inStock >= requestedQuantity;

  return {
    valid: available,
    message: available
      ? `${requestedQuantity} items available`
      : `Only ${inv.inStock} items available, requested ${requestedQuantity}`,
    availableStock: inv.inStock,
    requestedQuantity
  };
}

/**
 * Update product stock in database (reduce stock)
 */
export async function reduceProductStock(productId: string, quantity: number): Promise<boolean> {
  try {
    // Check current stock first
    const product = await Product.findById(productId);
    if (!product) {
      console.error(`Product not found: ${productId}`);
      return false;
    }
    
    const currentStock = product.inventory?.inStock || 0;
    if (currentStock < quantity) {
      console.warn(`Insufficient stock for product ${productId}: requested ${quantity}, available ${currentStock}`);
      // Return false to indicate insufficient stock
      return false;
    }
    
    // Update inventory stock
    const result = await Product.findByIdAndUpdate(
      productId,
      { $inc: { 'inventory.inStock': -quantity } },
      { new: true }
    );
    
    if (result) {
      console.log(`Updated stock for product ${productId}: reduced by ${quantity}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error reducing stock for product ${productId}:`, error);
    return false;
  }
}

/**
 * Restore product stock in database (increase stock) - useful for order cancellations
 */
export async function restoreProductStock(productId: string, quantity: number): Promise<boolean> {
  try {
    const result = await Product.findByIdAndUpdate(
      productId,
      { $inc: { 'inventory.inStock': quantity } },
      { new: true }
    );
    
    if (result) {
      console.log(`Restored stock for product ${productId}: increased by ${quantity}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error restoring stock for product ${productId}:`, error);
    return false;
  }
}

/**
 * Validate stock for multiple products (useful for cart validation)
 */
export async function validateCartStock(items: Array<{ productId: string; quantity: number; name: string }>): Promise<{
  valid: boolean;
  errors: string[];
}> {
  const errors: string[] = [];
  
  for (const item of items) {
    try {
      const product = await Product.findById(item.productId);
      if (!product) {
        errors.push(`Product not found: ${item.name}`);
        continue;
      }
      
      const validation = validateStockQuantity(product.inventory, item.quantity);
      if (!validation.valid) {
        errors.push(`${item.name}: ${validation.message}`);
      }
    } catch (error) {
      errors.push(`Error checking stock for ${item.name}`);
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}
