'use client';

import { useState, useRef } from 'react';
import { 
  Upload, 
  Download, 
  FileText, 
  AlertTriangle, 
  CheckCircle, 
  Info, 
  Loader2, 
  HelpCircle,
  X
} from 'lucide-react';

export default function BulkImportExportPage() {
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [exportStatus, setExportStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState<string>('');
  const [uploadDetails, setUploadDetails] = useState<{
    total: number;
    created: number;
    updated: number;
    failed: number;
    errorDetails: string[];
  } | null>(null);
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Sample template data for the CSV
  const templateData = [
    { field: 'name', required: true, description: 'Product name', example: 'Blue Sapphire Ring' },
    { field: 'description', required: true, description: 'Product description', example: 'Beautiful blue sapphire ring with diamond accents' },
    { field: 'price', required: true, description: 'Product price (in USD)', example: '2599.99' },
    { field: 'category', required: true, description: 'Product category', example: 'Rings' },
    { field: 'inventory.inStock', required: false, description: 'Current stock level', example: '10' },
    { field: '_id', required: false, description: 'Product ID (include only for updating existing products)', example: '61f83e5cd66aaa3456789012' },
  ];

  // Function to handle CSV file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Create a FormData object to send the file
    const formData = new FormData();
    formData.append('file', file);

    setUploadStatus('loading');
    setMessage('Uploading and processing your file...');
    setUploadDetails(null);

    try {
      const response = await fetch('/api/products/bulk-import', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to import products');
      }

      setUploadStatus('success');
      setMessage(result.message || 'Products imported successfully');
      setUploadDetails({
        total: result.total || 0,
        created: result.created || 0,
        updated: result.updated || 0,
        failed: result.failed || 0,
        errorDetails: result.errorDetails || [],
      });
    } catch (error: any) {
      setUploadStatus('error');
      setMessage(error.message || 'An error occurred during import');
    } finally {
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Function to export products as CSV
  const handleExport = async () => {
    setExportStatus('loading');
    setMessage('Generating CSV file...');

    try {
      const response = await fetch('/api/products/bulk-export', {
        method: 'GET',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to export products');
      }

      // Get the CSV content from the response
      const csvContent = await response.text();

      // Create a blob and download the file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `products-export-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setExportStatus('success');
      setMessage('Products exported successfully');

      // Reset status after 3 seconds
      setTimeout(() => {
        setExportStatus('idle');
        setMessage('');
      }, 3000);
    } catch (error: any) {
      setExportStatus('error');
      setMessage(error.message || 'An error occurred during export');
    }
  };

  // Function to download a CSV template
  const handleDownloadTemplate = () => {
    // Create headers row
    const headers = templateData.map(item => item.field).join(',');
    
    // Create example data row
    const exampleData = templateData.map(item => item.example).join(',');
    
    // Combine into CSV content
    const csvContent = `${headers}\n${exampleData}`;
    
    // Create a blob and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'product-import-template.csv');
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="max-w-7xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Bulk Import/Export Products</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Import Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold flex items-center">
              <Upload className="mr-2 text-blue-500" size={20} />
              Import Products
            </h2>
            <button
              onClick={() => setIsTemplateModalOpen(true)}
              className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
            >
              <HelpCircle size={16} className="mr-1" />
              CSV Format Help
            </button>
          </div>
          
          <p className="text-gray-600 mb-4">
            Upload a CSV file to bulk import products. You can add new products or update existing ones.
          </p>
          
          <div className="mb-6">
            <label 
              htmlFor="csvFile" 
              className="block w-full cursor-pointer text-center py-8 px-4 border-2 border-dashed border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <FileText size={36} className="mx-auto mb-2 text-gray-400" />
              <span className="text-sm text-gray-600 block mb-1">Drop your CSV file here or click to browse</span>
              <span className="text-xs text-gray-500">Supported format: .csv</span>
              <input
                id="csvFile"
                type="file"
                accept=".csv"
                ref={fileInputRef}
                onChange={handleFileUpload}
                className="hidden"
              />
            </label>
          </div>
          
          <div className="flex justify-between items-center">
            <button
              onClick={handleDownloadTemplate}
              className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
            >
              <Download size={16} className="mr-1" />
              Download Template
            </button>
            
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={uploadStatus === 'loading'}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {uploadStatus === 'loading' ? (
                <>
                  <Loader2 size={16} className="mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload size={16} className="mr-2" />
                  Upload File
                </>
              )}
            </button>
          </div>
          
          {uploadStatus !== 'idle' && (
            <div className={`mt-4 p-4 rounded-md ${
              uploadStatus === 'success' ? 'bg-green-50 text-green-700' :
              uploadStatus === 'error' ? 'bg-red-50 text-red-700' :
              'bg-blue-50 text-blue-700'
            }`}>
              <div className="flex items-center mb-2">
                {uploadStatus === 'success' && <CheckCircle size={18} className="mr-2" />}
                {uploadStatus === 'error' && <AlertTriangle size={18} className="mr-2" />}
                {uploadStatus === 'loading' && <Loader2 size={18} className="mr-2 animate-spin" />}
                <p className="font-medium">{message}</p>
              </div>
              
              {uploadDetails && (
                <div className="mt-2 text-sm">
                  <p>Total products processed: {uploadDetails.total}</p>
                  <p>New products created: {uploadDetails.created}</p>
                  <p>Existing products updated: {uploadDetails.updated}</p>
                  {uploadDetails.failed > 0 && (
                    <div className="mt-2">
                      <p className="text-red-600 font-medium">{uploadDetails.failed} products failed to import:</p>
                      <ul className="list-disc list-inside mt-1">
                        {uploadDetails.errorDetails.map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
        
        {/* Export Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold flex items-center mb-4">
            <Download className="mr-2 text-green-500" size={20} />
            Export Products
          </h2>
          
          <p className="text-gray-600 mb-8">
            Download all products as a CSV file. You can edit this file and then import it back to update your products.
          </p>
          
          <div className="flex justify-center mb-8">
            <button
              onClick={handleExport}
              disabled={exportStatus === 'loading'}
              className="px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {exportStatus === 'loading' ? (
                <>
                  <Loader2 size={16} className="mr-2 animate-spin" />
                  Generating CSV...
                </>
              ) : (
                <>
                  <Download size={16} className="mr-2" />
                  Export All Products
                </>
              )}
            </button>
          </div>
          
          {exportStatus === 'success' && (
            <div className="p-4 bg-green-50 text-green-700 rounded-md flex items-center">
              <CheckCircle size={18} className="mr-2" />
              <p>{message}</p>
            </div>
          )}
          
          {exportStatus === 'error' && (
            <div className="p-4 bg-red-50 text-red-700 rounded-md flex items-center">
              <AlertTriangle size={18} className="mr-2" />
              <p>{message}</p>
            </div>
          )}
          
          <div className="mt-8 p-4 bg-blue-50 text-blue-700 rounded-md">
            <div className="flex items-center mb-2">
              <Info size={18} className="mr-2" />
              <p className="font-medium">What's included in the export?</p>
            </div>
            <ul className="list-disc list-inside text-sm ml-6">
              <li>Product name, description, and price</li>
              <li>Category information</li>
              <li>Current inventory levels</li>
              <li>Product IDs (needed for updating existing products)</li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* CSV Format Help Modal */}
      {isTemplateModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-6 border-b">
              <h3 className="text-xl font-semibold">CSV Import Format</h3>
              <button 
                onClick={() => setIsTemplateModalOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>
            
            <div className="p-6">
              <p className="mb-4">
                Your CSV file should include the following columns. Fields marked as required must have a value.
              </p>
              
              <div className="overflow-x-auto">
                <table className="min-w-full border border-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Field</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Required</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Example</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {templateData.map((item, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.field}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {item.required ? (
                            <span className="text-red-600">Yes</span>
                          ) : (
                            <span className="text-gray-400">No</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.description}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.example}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              <div className="mt-6 p-4 bg-gray-50 rounded-md">
                <h4 className="font-medium mb-2">Guidelines:</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Use commas to separate fields. If a field contains commas, enclose it in double quotes.</li>
                  <li>To update existing products, include the "_id" field with the product's MongoDB ID.</li>
                  <li>Without an "_id" field, a new product will be created.</li>
                  <li>Price should be a number without currency symbols (e.g., 99.99, not $99.99).</li>
                  <li>Inventory fields are optional. If not provided, defaults will be used.</li>
                  <li>Make sure category names match your existing categories.</li>
                </ul>
              </div>
            </div>
            
            <div className="p-6 border-t flex justify-end">
              <button
                onClick={handleDownloadTemplate}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mr-4"
              >
                Download Template
              </button>
              <button
                onClick={() => setIsTemplateModalOpen(false)}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 